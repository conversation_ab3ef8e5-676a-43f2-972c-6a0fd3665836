# Android Namespace Isolation Fix for <PERSON><PERSON><PERSON><PERSON><PERSON>

## Problem Description

Your LeviLauncher can load independent shared objects as mods, but fails to load `libDynamicLights.so` because it depends on `libCrestHelper.so`. Android's namespace isolation prevents cross-library dependency loading when using `System.load()`.

**Dependency Analysis:**
- `libCrestHelper.so` - Independent library (loads successfully)
- `libDynamicLights.so` - Depends on `libCrestHelper.so` (fails to load due to namespace isolation)

## Root Cause

Starting from Android 7.0 (API 24), Android introduced linker namespaces to isolate shared libraries. When using `System.load()` from Java, each library is loaded in its own isolated namespace, preventing dependent libraries from finding their dependencies.

## Solution Implemented

### 1. Native Mod Loader with Namespace Bypass

Created a JNI-based mod loader (`native_mod_loader.cpp`) that:

- Uses `android_dlopen_ext()` with custom namespace configuration
- Loads dependencies with `RTLD_GLOBAL` flag to make symbols available
- Falls back to regular `dlopen()` if namespace functions are unavailable
- Implements dependency resolution for known mod relationships

### 2. Key Features

**Namespace Creation:**
- Creates custom namespace with permissive paths
- Falls back to existing namespaces (vndk, sphal) if creation fails
- Handles different Android versions (8.0+ vs 11.0+)

**Dependency Resolution:**
- Automatically detects that `libDynamicLights.so` depends on `libCrestHelper.so`
- Loads dependencies first with `RTLD_GLOBAL` flag
- Loads dependent libraries after dependencies are resolved

**Fallback Mechanism:**
- If native loader fails, falls back to original `System.load()` method
- Graceful degradation ensures compatibility

### 3. Files Modified/Created

1. **`app/src/main/cpp/leviutils/include/dlext_namespaces.h`** - Android namespace API definitions
2. **`app/src/main/cpp/leviutils/src/mod_loader/native_mod_loader.cpp`** - Native implementation
3. **`app/src/main/java/org/levimc/launcher/core/mods/ModNativeLoader.java`** - Updated Java interface
4. **`app/src/main/cpp/leviutils/xmake.lua`** - Updated build configuration

## How It Works

### Loading Process:

1. **Initialize Native Loader:**
   - Get function pointers for `android_dlopen_ext`, `android_create_namespace`, etc.
   - Create custom namespace or obtain existing permissive namespace

2. **Load Mods with Dependencies:**
   - For `libDynamicLights.so`: First load `libCrestHelper.so` with `RTLD_GLOBAL`
   - Then load `libDynamicLights.so` which can now find symbols from `libCrestHelper.so`
   - For independent mods: Load directly

3. **Fallback:**
   - If native loading fails, use original `System.load()` method

### Technical Details:

```cpp
// Create namespace with permissive paths
android_create_namespace(
    "mod_namespace",
    "/data/data:/system/lib64:/system/lib:/vendor/lib64:/vendor/lib",
    "/system/lib64:/system/lib:/vendor/lib64:/vendor/lib",
    ANDROID_NAMESPACE_TYPE_SHARED | ANDROID_NAMESPACE_TYPE_ISOLATED,
    "/data/data:/system/:/vendor/",
    nullptr
);

// Load dependency with RTLD_GLOBAL
android_dlopen_ext("libCrestHelper.so", RTLD_NOW | RTLD_GLOBAL, &extinfo);

// Load dependent library
android_dlopen_ext("libDynamicLights.so", RTLD_NOW, &extinfo);
```

## Alternative Solutions Considered

### 1. Load Order Management (Simpler)
Load `libCrestHelper.so` first with `RTLD_GLOBAL` using regular `dlopen()`:

```java
// Load dependency first
System.load("/path/to/libCrestHelper.so");
// Then load dependent library
System.load("/path/to/libDynamicLights.so");
```

### 2. Static Linking
Link `libCrestHelper.so` statically into `libDynamicLights.so` to eliminate runtime dependency.

### 3. Symbol Export
Modify `libCrestHelper.so` to export symbols globally when loaded.

## Testing Instructions

1. **Build the Project:**
   ```bash
   ./gradlew assembleDebug
   ```

2. **Install and Test:**
   - Install the APK on an Android device
   - Place both `libCrestHelper.so` and `libDynamicLights.so` in the mods directory
   - Enable both mods in the launcher
   - Launch the game and check logs

3. **Verify Success:**
   - Check logcat for "Successfully loaded ... with native loader" messages
   - Verify that both mods are functioning in-game
   - No "UnsatisfiedLinkError" should occur

## Troubleshooting

### If Native Loader Fails:
- Check logcat for detailed error messages
- Verify Android version compatibility (works best on Android 8.0+)
- Ensure both mod files are present and readable

### If Fallback Also Fails:
- This indicates the original namespace isolation issue
- Consider using alternative solutions (load order management)
- Check mod file integrity and architecture compatibility

## Compatibility

- **Android 8.0-10.0:** Full namespace bypass support
- **Android 11.0+:** Limited support (uses offset method for private functions)
- **Android 7.0-7.1:** Fallback to original method (namespace isolation still present)

## Security Considerations

This solution bypasses Android's namespace isolation, which is a security feature. Use only for trusted mods and consider the security implications in production environments.
