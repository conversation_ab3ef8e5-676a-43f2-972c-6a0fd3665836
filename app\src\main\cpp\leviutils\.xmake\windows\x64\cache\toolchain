{
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android",
            arch = "armeabi-v7a"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndkver = 25,
        __global = true,
        arch = "armeabi-v7a",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        cross = "arm-linux-androideabi-",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sdkver = "21"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android",
            arch = "armeabi-v7a"
        }
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    }
}