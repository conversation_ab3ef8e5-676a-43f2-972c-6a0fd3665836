{
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-insert-timestamp"] = true,
            ["--strip-debug"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-tsaware"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-no-seh"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-o"] = true,
            ["-dn"] = true,
            ["-L"] = true,
            ["-s"] = true,
            ["--export-all-symbols"] = true,
            ["-static"] = true,
            ["--Bstatic"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--no-whole-archive"] = true,
            ["--gc-sections"] = true,
            ["--allow-multiple-definition"] = true,
            ["-m"] = true,
            ["--demangle"] = true,
            ["--enable-auto-import"] = true,
            ["-dy"] = true,
            ["--high-entropy-va"] = true,
            ["--appcontainer"] = true,
            ["--shared"] = true,
            ["--no-demangle"] = true,
            ["-l"] = true,
            ["--verbose"] = true,
            ["--kill-at"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-dynamicbase"] = true,
            ["-S"] = true,
            ["--whole-archive"] = true,
            ["--nxcompat"] = true,
            ["--Bdynamic"] = true,
            ["-v"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-nxcompat"] = true,
            ["--dynamicbase"] = true,
            ["--help"] = true,
            ["--version"] = true,
            ["--tsaware"] = true,
            ["--fatal-warnings"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--strip-all"] = true,
            ["--no-seh"] = true,
            ["--disable-dynamicbase"] = true,
            ["--large-address-aware"] = true
        }
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-MMD -MF"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=aarch64-none-linux-android21 -nostdlib++ -llog --target=aarch64-none-linux-android21 -nostdlib++_-fPIC"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-std=c++20"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-fPIC"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-Wno-gnu-line-marker -Werror"] = false,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-O3"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-DNDEBUG"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-Oz"] = true
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            cross = "aarch64-linux-android-",
            sdkver = "21",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-iprefix"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fpch-codegen"] = true,
            ["-mstack-arg-probe"] = true,
            ["-isystem"] = true,
            ["-fsanitize-stats"] = true,
            ["-gmodules"] = true,
            ["-module-file-info"] = true,
            ["-fforce-enable-int128"] = true,
            ["-mlong-double-80"] = true,
            ["-gdwarf-3"] = true,
            ["-foffload-lto"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fshort-wchar"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-femulated-tls"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fstack-usage"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fobjc-weak"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fsystem-module"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-emit-llvm"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mlvi-cfi"] = true,
            ["-mmemops"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-ffast-math"] = true,
            ["-Xlinker"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fsanitize-trap"] = true,
            ["-fstack-clash-protection"] = true,
            ["-U"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fmodules-ts"] = true,
            ["-ftrapv"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fconvergent-functions"] = true,
            ["-miamcu"] = true,
            ["-mibt-seal"] = true,
            ["-cl-mad-enable"] = true,
            ["-fno-rtti"] = true,
            ["-fno-split-stack"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fverbose-asm"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fxray-instrument"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-strict-return"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-cl-opt-disable"] = true,
            ["-ffixed-x17"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fno-discard-value-names"] = true,
            ["-gdwarf-2"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-mno-embedded-data"] = true,
            ["-MD"] = true,
            ["-fno-common"] = true,
            ["-ffixed-x4"] = true,
            ["-gdwarf"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-pipe"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["--hip-link"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-time"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mllvm"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-d1"] = true,
            ["-ffixed-x14"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffixed-x29"] = true,
            ["-dependency-dot"] = true,
            ["-mcumode"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-ffixed-x22"] = true,
            ["-mno-hvx"] = true,
            ["-working-directory"] = true,
            ["-ffixed-x5"] = true,
            ["-mnop-mcount"] = true,
            ["-fsigned-char"] = true,
            ["-mno-code-object-v3"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fdata-sections"] = true,
            ["-mglobal-merge"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-ffixed-r9"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-Qn"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["--cuda-device-only"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-jump-tables"] = true,
            ["-rpath"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fstandalone-debug"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-mno-execute-only"] = true,
            ["--cuda-host-only"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-arch"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-gline-tables-only"] = true,
            ["-ffixed-a5"] = true,
            ["-ffixed-x16"] = true,
            ["-fmerge-all-constants"] = true,
            ["-mno-msa"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-finline-functions"] = true,
            ["-T"] = true,
            ["-mhvx"] = true,
            ["-fjump-tables"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-mno-neg-immediates"] = true,
            ["-extract-api"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-mcrc"] = true,
            ["-fopenmp-simd"] = true,
            ["-fvectorize"] = true,
            ["-ffixed-x23"] = true,
            ["-faligned-allocation"] = true,
            ["--verify-debug-info"] = true,
            ["-fstack-protector-all"] = true,
            ["-fwritable-strings"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-mlong-double-64"] = true,
            ["-fansi-escape-codes"] = true,
            ["-traditional-cpp"] = true,
            ["-mpacked-stack"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-short-wchar"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["--help-hidden"] = true,
            ["-ffixed-a1"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-a2"] = true,
            ["-iwithsysroot"] = true,
            ["-fminimize-whitespace"] = true,
            ["-M"] = true,
            ["-imacros"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-dependency-file"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fasync-exceptions"] = true,
            ["-mno-memops"] = true,
            ["-nohipwrapperinc"] = true,
            ["-ffixed-a3"] = true,
            ["-mnocrc"] = true,
            ["-fno-builtin"] = true,
            ["-mno-cumode"] = true,
            ["-nogpuinc"] = true,
            ["-dI"] = true,
            ["-mmadd4"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fglobal-isel"] = true,
            ["-print-effective-triple"] = true,
            ["-mrtd"] = true,
            ["-fno-exceptions"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-ffixed-x12"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fno-temp-file"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fcxx-exceptions"] = true,
            ["-x"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mlocal-sdata"] = true,
            ["-mno-crc"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fenable-matrix"] = true,
            ["-fblocks"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mno-packets"] = true,
            ["-fno-autolink"] = true,
            ["-print-multiarch"] = true,
            ["-mlong-double-128"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-w"] = true,
            ["-mms-bitfields"] = true,
            ["-fapplication-extension"] = true,
            ["-mno-abicalls"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fdebug-types-section"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fdebug-macro"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mbackchain"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-local-sdata"] = true,
            ["-v"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-mstackrealign"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-signed-zeros"] = true,
            ["-faddrsig"] = true,
            ["-fno-access-control"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mmt"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-ffixed-d3"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-L"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-nogpulib"] = true,
            ["-mpackets"] = true,
            ["-fms-extensions"] = true,
            ["-verify-pch"] = true,
            ["-print-supported-cpus"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-mnvs"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-trigraphs"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x9"] = true,
            ["-shared-libsan"] = true,
            ["-fapple-kext"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fstack-size-section"] = true,
            ["-nostdinc"] = true,
            ["-fno-digraphs"] = true,
            ["-ffixed-x8"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-pg"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mnvj"] = true,
            ["-mmsa"] = true,
            ["-fno-show-source-location"] = true,
            ["-ffixed-d5"] = true,
            ["-print-search-dirs"] = true,
            ["-ffinite-loops"] = true,
            ["-dD"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-F"] = true,
            ["-fapprox-func"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-msvr4-struct-return"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-integrated-as"] = true,
            ["-Qunused-arguments"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-mseses"] = true,
            ["-fno-new-infallible"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fcs-profile-generate"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-ffixed-d6"] = true,
            ["-iwithprefix"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-E"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-plt"] = true,
            ["-ffixed-x19"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-o"] = true,
            ["-ffixed-x11"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-P"] = true,
            ["-fstack-protector-strong"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mno-movt"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-mfentry"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-relocatable-pch"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-x28"] = true,
            ["-mrecord-mcount"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-iquote"] = true,
            ["-fno-memory-profile"] = true,
            ["-mcmse"] = true,
            ["-fzvector"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-ffixed-r19"] = true,
            ["-isystem-after"] = true,
            ["-fsycl"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-save-stats"] = true,
            ["-fshort-enums"] = true,
            ["-mno-outline"] = true,
            ["-fstack-protector"] = true,
            ["-trigraphs"] = true,
            ["-emit-interface-stubs"] = true,
            ["-ivfsoverlay"] = true,
            ["-MF"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fexceptions"] = true,
            ["-fno-sycl"] = true,
            ["-ffixed-x31"] = true,
            ["-undef"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mno-nvj"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-ffixed-x24"] = true,
            ["-gembed-source"] = true,
            ["-gdwarf64"] = true,
            ["-emit-module"] = true,
            ["-gdwarf32"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-malign-double"] = true,
            ["-gdwarf-5"] = true,
            ["-ffixed-x18"] = true,
            ["-MP"] = true,
            ["-fobjc-arc"] = true,
            ["-moutline-atomics"] = true,
            ["-fprotect-parens"] = true,
            ["-ffixed-d0"] = true,
            ["-ffixed-d2"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-mno-long-calls"] = true,
            ["-fmodules-decluse"] = true,
            ["-I-"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mno-relax"] = true,
            ["-msave-restore"] = true,
            ["--gpu-bundle-output"] = true,
            ["-ffixed-x6"] = true,
            ["-fropi"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-meabi"] = true,
            ["-funroll-loops"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mtgsplit"] = true,
            ["-mno-implicit-float"] = true,
            ["-fmemory-profile"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fstrict-enums"] = true,
            ["-I"] = true,
            ["--migrate"] = true,
            ["-mno-tgsplit"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-MV"] = true,
            ["-fsized-deallocation"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fnew-infallible"] = true,
            ["-ftime-trace"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-mno-restrict-it"] = true,
            ["-fcall-saved-x18"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-print-target-triple"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ftrigraphs"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mlvi-hardening"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-include-pch"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-S"] = true,
            ["-fignore-exceptions"] = true,
            ["-mgpopt"] = true,
            ["-fno-use-init-array"] = true,
            ["-fms-compatibility"] = true,
            ["-mfp64"] = true,
            ["-Tdata"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fuse-line-directives"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffixed-a6"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-c"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-Ttext"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-freg-struct-return"] = true,
            ["-gcodeview"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fopenmp"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-print-runtime-dir"] = true,
            ["-fobjc-exceptions"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fcommon"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-C"] = true,
            ["-membedded-data"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-show-column"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-include"] = true,
            ["-g"] = true,
            ["-ibuiltininc"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-mfp32"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-signed-char"] = true,
            ["-fpascal-strings"] = true,
            ["-fno-declspec"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-ffreestanding"] = true,
            ["--config"] = true,
            ["-ffixed-d4"] = true,
            ["-flto"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fmodules"] = true,
            ["-emit-merged-ifs"] = true,
            ["-freroll-loops"] = true,
            ["-fno-operator-names"] = true,
            ["-fcf-protection"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-MG"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-rtti-data"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["--emit-static-lib"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-femit-all-decls"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-pthread"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fcall-saved-x9"] = true,
            ["-ffixed-x20"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fcall-saved-x12"] = true,
            ["-ffixed-point"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-B"] = true,
            ["-fgnu89-inline"] = true,
            ["-fsplit-stack"] = true,
            ["-fdiscard-value-names"] = true,
            ["-Wdeprecated"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-mrelax"] = true,
            ["-ffixed-x7"] = true,
            ["-MMD"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fdigraphs"] = true,
            ["-fno-global-isel"] = true,
            ["-dM"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fembed-bitcode"] = true,
            ["-fdeclspec"] = true,
            ["-mextern-sdata"] = true,
            ["-CC"] = true,
            ["-mno-madd4"] = true,
            ["-mthread-model"] = true,
            ["-munaligned-access"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-MT"] = true,
            ["-dsym-dir"] = true,
            ["-fintegrated-as"] = true,
            ["-ffixed-x13"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mno-save-restore"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fpch-debuginfo"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-Xopenmp-target"] = true,
            ["-fcxx-modules"] = true,
            ["-Xassembler"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-H"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-ffixed-x2"] = true,
            ["-mno-global-merge"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fno-spell-checking"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-emit-ast"] = true,
            ["-pedantic"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-xray-function-index"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-save-temps"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-mabicalls"] = true,
            ["-freciprocal-math"] = true,
            ["-mno-mt"] = true,
            ["--analyze"] = true,
            ["-ffixed-x21"] = true,
            ["-isysroot"] = true,
            ["-fno-stack-protector"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-ffunction-sections"] = true,
            ["-ffixed-x15"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fborland-extensions"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-offload-lto"] = true,
            ["-G"] = true,
            ["-fxray-link-deps"] = true,
            ["-fseh-exceptions"] = true,
            ["-z"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-nobuiltininc"] = true,
            ["-Xanalyzer"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-profile-generate"] = true,
            ["-frwpi"] = true,
            ["-help"] = true,
            ["-index-header-map"] = true,
            ["-Tbss"] = true,
            ["-ffixed-x26"] = true,
            ["-mrelax-all"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-MJ"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-static-libsan"] = true,
            ["-cxx-isystem"] = true,
            ["-fgpu-rdc"] = true,
            ["-cl-no-stdinc"] = true,
            ["-ffixed-x25"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-ffixed-x3"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-print-resource-dir"] = true,
            ["-fno-debug-macro"] = true,
            ["-Qy"] = true,
            ["-mno-nvs"] = true,
            ["-MM"] = true,
            ["-gno-embed-source"] = true,
            ["-mwavefrontsize64"] = true,
            ["-ffixed-x27"] = true,
            ["-static-openmp"] = true,
            ["--precompile"] = true,
            ["-msoft-float"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-D"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-mrestrict-it"] = true,
            ["-mmark-bti-property"] = true,
            ["-print-targets"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-moutline"] = true,
            ["-module-dependency-dir"] = true,
            ["-idirafter"] = true,
            ["-mno-seses"] = true,
            ["-gline-directives-only"] = true,
            ["-MQ"] = true,
            ["-fmath-errno"] = true,
            ["--analyzer-output"] = true,
            ["-fmodules-search-all"] = true,
            ["-ffixed-x30"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-cl-finite-math-only"] = true,
            ["--version"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mno-gpopt"] = true,
            ["-mlong-calls"] = true,
            ["-fno-lto"] = true,
            ["-b"] = true,
            ["-rewrite-objc"] = true,
            ["-objcmt-allowlist-dir-path"] = true
        }
    }
}