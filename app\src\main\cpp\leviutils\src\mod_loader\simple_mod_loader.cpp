#include <jni.h>
#include <dlfcn.h>
#include <android/log.h>
#include <string>
#include <vector>
#include <map>

#define LOG_TAG "SimpleModLoader"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

class SimpleModLoader {
private:
    std::map<std::string, void*> loaded_libraries;
    bool crash_protection_enabled = true;
    
public:
    void* loadLibrary(const std::string& path, bool global = true) {
        // Check if already loaded
        auto it = loaded_libraries.find(path);
        if (it != loaded_libraries.end()) {
            LOGD("Library %s already loaded", path.c_str());
            return it->second;
        }

        // Check if file exists and is readable
        FILE* file = fopen(path.c_str(), "rb");
        if (!file) {
            LOGE("Cannot access file %s", path.c_str());
            return nullptr;
        }
        fclose(file);

        // Clear any previous dlopen errors
        dlerror();

        int flags = RTLD_NOW;
        if (global) {
            flags |= RTLD_GLOBAL; // Make symbols available to other libraries
        }

        LOGD("Loading library %s with flags 0x%x", path.c_str(), flags);

        // Try loading with error handling
        void* handle = nullptr;
        try {
            handle = dlopen(path.c_str(), flags);
        } catch (...) {
            LOGE("Exception occurred while loading %s", path.c_str());
            return nullptr;
        }

        if (handle) {
            LOGI("Successfully loaded %s at address %p", path.c_str(), handle);
            loaded_libraries[path] = handle;

            // Verify the library is properly loaded by checking a symbol
            void* test_symbol = dlsym(handle, "_init");
            LOGD("Library %s _init symbol: %p", path.c_str(), test_symbol);
        } else {
            const char* error = dlerror();
            LOGE("Failed to load %s: %s", path.c_str(), error ? error : "Unknown error");
        }

        return handle;
    }
    
    bool loadModWithDependencies(const std::string& modPath) {
        std::string modDir = modPath.substr(0, modPath.find_last_of('/'));
        std::string modName = modPath.substr(modPath.find_last_of('/') + 1);
        
        LOGI("Loading mod: %s", modName.c_str());
        
        // Special handling for libDynamicLights.so - load libCrestHelper.so first
        if (modName == "libDynamicLights.so") {
            std::string crestHelperPath = modDir + "/libCrestHelper.so";
            LOGI("Loading dependency libCrestHelper.so first");
            
            void* crestHandle = loadLibrary(crestHelperPath, true); // Load with RTLD_GLOBAL
            if (!crestHandle) {
                LOGE("Failed to load dependency libCrestHelper.so");
                return false;
            }
        }
        
        // Now load the main mod
        void* modHandle = loadLibrary(modPath, false);
        return modHandle != nullptr;
    }
    
    void unloadAll() {
        for (auto& pair : loaded_libraries) {
            if (pair.second) {
                dlclose(pair.second);
            }
        }
        loaded_libraries.clear();
    }
};

// Global instance
static SimpleModLoader g_simple_loader;

extern "C" {

JNIEXPORT jboolean JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeLoadModSimple(JNIEnv *env, jclass clazz, jstring path) {
    const char* nativePath = env->GetStringUTFChars(path, nullptr);
    if (!nativePath) {
        return JNI_FALSE;
    }
    
    bool result = g_simple_loader.loadModWithDependencies(std::string(nativePath));
    
    env->ReleaseStringUTFChars(path, nativePath);
    return result ? JNI_TRUE : JNI_FALSE;
}

JNIEXPORT void JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeUnloadAllSimple(JNIEnv *env, jclass clazz) {
    g_simple_loader.unloadAll();
}

} // extern "C"
