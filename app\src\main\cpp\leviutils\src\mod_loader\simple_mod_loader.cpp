#include <jni.h>
#include <dlfcn.h>
#include <android/log.h>
#include <string>
#include <vector>
#include <map>

#define LOG_TAG "SimpleModLoader"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

class SimpleModLoader {
private:
    std::map<std::string, void*> loaded_libraries;
    
public:
    void* loadLibrary(const std::string& path, bool global = true) {
        // Check if already loaded
        auto it = loaded_libraries.find(path);
        if (it != loaded_libraries.end()) {
            LOGD("Library %s already loaded", path.c_str());
            return it->second;
        }
        
        int flags = RTLD_NOW;
        if (global) {
            flags |= RTLD_GLOBAL; // Make symbols available to other libraries
        }
        
        LOGD("Loading library %s with flags 0x%x", path.c_str(), flags);
        void* handle = dlopen(path.c_str(), flags);
        
        if (handle) {
            LOGI("Successfully loaded %s", path.c_str());
            loaded_libraries[path] = handle;
        } else {
            LOGE("Failed to load %s: %s", path.c_str(), dlerror());
        }
        
        return handle;
    }
    
    bool loadModWithDependencies(const std::string& modPath) {
        std::string modDir = modPath.substr(0, modPath.find_last_of('/'));
        std::string modName = modPath.substr(modPath.find_last_of('/') + 1);
        
        LOGI("Loading mod: %s", modName.c_str());
        
        // Special handling for libDynamicLights.so - load libCrestHelper.so first
        if (modName == "libDynamicLights.so") {
            std::string crestHelperPath = modDir + "/libCrestHelper.so";
            LOGI("Loading dependency libCrestHelper.so first");
            
            void* crestHandle = loadLibrary(crestHelperPath, true); // Load with RTLD_GLOBAL
            if (!crestHandle) {
                LOGE("Failed to load dependency libCrestHelper.so");
                return false;
            }
        }
        
        // Now load the main mod
        void* modHandle = loadLibrary(modPath, false);
        return modHandle != nullptr;
    }
    
    void unloadAll() {
        for (auto& pair : loaded_libraries) {
            if (pair.second) {
                dlclose(pair.second);
            }
        }
        loaded_libraries.clear();
    }
};

// Global instance
static SimpleModLoader g_simple_loader;

extern "C" {

JNIEXPORT jboolean JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeLoadModSimple(JNIEnv *env, jclass clazz, jstring path) {
    const char* nativePath = env->GetStringUTFChars(path, nullptr);
    if (!nativePath) {
        return JNI_FALSE;
    }
    
    bool result = g_simple_loader.loadModWithDependencies(std::string(nativePath));
    
    env->ReleaseStringUTFChars(path, nativePath);
    return result ? JNI_TRUE : JNI_FALSE;
}

JNIEXPORT void JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeUnloadAllSimple(JNIEnv *env, jclass clazz) {
    g_simple_loader.unloadAll();
}

} // extern "C"
