# Testing the Namespace Isolation Fix

## Prerequisites

1. **Android SDK Setup:**
   ```bash
   # Create local.properties file with your Android SDK path
   echo "sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk" > local.properties
   # OR set environment variable
   export ANDROID_HOME=/path/to/android/sdk
   ```

2. **Build the Project:**
   ```bash
   ./gradlew assembleDebug
   ```

## Test Scenarios

### Scenario 1: Original Problem (Before Fix)
1. Place both `libCrestHelper.so` and `libDynamicLights.so` in mods directory
2. Enable both mods
3. Launch game
4. **Expected Result:** `libDynamicLights.so` fails to load with `UnsatisfiedLinkError`

### Scenario 2: With Native Loader Fix (After Fix)
1. Same setup as Scenario 1
2. Launch game with updated code
3. **Expected Result:** Both mods load successfully

### Scenario 3: Fallback Testing
1. Test on different Android versions
2. Verify graceful fallback to simple loader or System.load()

## Log Analysis

### Success Indicators:
```
I/NativeModLoader: Native mod loader initialized successfully
I/NativeModLoader: Loading libDynamicLights.so using advanced native loader
I/SimpleModLoader: Loading dependency libCrestHelper.so first
I/SimpleModLoader: Successfully loaded libCrestHelper.so
I/SimpleModLoader: Successfully loaded libDynamicLights.so
I/ModNativeLoader: Successfully loaded libDynamicLights.so with advanced native loader
```

### Fallback Indicators:
```
E/NativeModLoader: Advanced native loader failed, trying simple loader
I/SimpleModLoader: Loading dependency libCrestHelper.so first
I/ModNativeLoader: Successfully loaded libDynamicLights.so with simple native loader
```

### Failure Indicators:
```
E/ModNativeLoader: Can't load libDynamicLights.so: dlopen failed: library "libCrestHelper.so" not found
```

## Manual Testing Steps

1. **Install APK:**
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

2. **Push Mod Files:**
   ```bash
   adb push libCrestHelper.so /sdcard/Android/data/org.levimc.launcher/files/mods/
   adb push libDynamicLights.so /sdcard/Android/data/org.levimc.launcher/files/mods/
   ```

3. **Monitor Logs:**
   ```bash
   adb logcat | grep -E "(ModNativeLoader|NativeModLoader|SimpleModLoader)"
   ```

4. **Test Loading:**
   - Open LeviLauncher
   - Go to mods section
   - Enable both mods
   - Launch Minecraft
   - Check if both mods are working

## Verification Methods

### 1. Log Verification
Check for successful loading messages in logcat

### 2. Symbol Resolution Test
Create a simple test that calls functions from both libraries to verify symbols are properly resolved

### 3. Game Functionality Test
Verify that the actual mod functionality works in-game

## Troubleshooting

### Build Issues:
- Ensure Android SDK is properly configured
- Check NDK version compatibility
- Verify xmake is installed and working

### Runtime Issues:
- Check Android version compatibility
- Verify mod file architecture matches device
- Ensure proper file permissions

### Loading Issues:
- Check if both mod files are present
- Verify file integrity
- Test with individual mods first

## Alternative Quick Fix

If the native loader approach doesn't work, you can try this simple modification to the original code:

```java
// In ModNativeLoader.java, modify loadEnabledSoMods method:
public static void loadEnabledSoMods(ModManager modManager, File cacheDir) {
    List<Mod> mods = modManager.getMods();
    
    // First pass: Load dependencies
    for (Mod mod : mods) {
        if (!mod.isEnabled()) continue;
        if (mod.getFileName().equals("libCrestHelper.so")) {
            File src = new File(modManager.getCurrentVersion().modsDir, mod.getFileName());
            File dir = new File(cacheDir, "mods");
            if (!dir.exists()) dir.mkdirs();
            File dst = new File(dir, mod.getFileName());
            try {
                copyFile(src, dst);
                System.load(dst.getAbsolutePath());
                Logger.get().info("Loaded dependency: " + dst.getName());
            } catch (IOException | UnsatisfiedLinkError e) {
                Logger.get().error("Can't load dependency " + src.getName() + ": " + e.getMessage());
            }
        }
    }
    
    // Second pass: Load dependent libraries
    for (Mod mod : mods) {
        if (!mod.isEnabled()) continue;
        if (!mod.getFileName().equals("libCrestHelper.so")) {
            // Load other mods after dependencies
            File src = new File(modManager.getCurrentVersion().modsDir, mod.getFileName());
            File dir = new File(cacheDir, "mods");
            File dst = new File(dir, mod.getFileName());
            try {
                copyFile(src, dst);
                System.load(dst.getAbsolutePath());
                Logger.get().info("Loaded mod: " + dst.getName());
            } catch (IOException | UnsatisfiedLinkError e) {
                Logger.get().error("Can't load " + src.getName() + ": " + e.getMessage());
            }
        }
    }
}
```

This ensures `libCrestHelper.so` is loaded before `libDynamicLights.so`, which might be sufficient to resolve the dependency issue in some cases.
