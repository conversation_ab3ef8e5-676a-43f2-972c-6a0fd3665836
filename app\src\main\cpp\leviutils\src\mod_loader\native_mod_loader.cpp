#include <jni.h>
#include <dlfcn.h>
#include <android/log.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <android/dlext.h>
#include "dlext_namespaces.h"

#define LOG_TAG "NativeModLoader"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)

class NativeModLoader {
private:
    struct android_namespace_t* mod_namespace = nullptr;
    std::map<std::string, void*> loaded_libraries;
    
    // Function pointers for namespace functions
    struct android_namespace_t* (*android_create_namespace_func)(const char*, const char*, const char*, uint64_t, const char*, struct android_namespace_t*) = nullptr;
    struct android_namespace_t* (*android_get_exported_namespace_func)(const char*) = nullptr;
    void* (*android_dlopen_ext_func)(const char*, int, const android_dlextinfo*) = nullptr;
    
    bool initializeNamespaceFunctions() {
        // Try to get the functions from libdl.so first (Android 8-10)
        android_create_namespace_func = reinterpret_cast<decltype(android_create_namespace_func)>(
            dlsym(RTLD_DEFAULT, "android_create_namespace"));
        
        android_get_exported_namespace_func = reinterpret_cast<decltype(android_get_exported_namespace_func)>(
            dlsym(RTLD_DEFAULT, "android_get_exported_namespace"));
            
        android_dlopen_ext_func = reinterpret_cast<decltype(android_dlopen_ext_func)>(
            dlsym(RTLD_DEFAULT, "android_dlopen_ext"));
        
        if (!android_dlopen_ext_func) {
            LOGE("Failed to get android_dlopen_ext function");
            return false;
        }
        
        // For Android 11+, android_get_exported_namespace is in libc.so but private
        // We'll try to get it via offset calculation if the public function fails
        if (!android_get_exported_namespace_func) {
            LOGD("android_get_exported_namespace not available, trying offset method");
            // Try to get it via offset from a known public symbol
            void* api_level_func = dlsym(RTLD_DEFAULT, "android_get_device_api_level");
            if (api_level_func) {
                // This offset might need adjustment for different Android versions
                // This is a known technique but fragile across versions
                android_get_exported_namespace_func = reinterpret_cast<decltype(android_get_exported_namespace_func)>(
                    reinterpret_cast<char*>(api_level_func) + 0x650e0);
                LOGD("Trying android_get_exported_namespace via offset");
            }
        }
        
        return true;
    }
    
    bool createModNamespace() {
        if (mod_namespace) {
            return true; // Already created
        }
        
        if (android_create_namespace_func) {
            // Android 8-10: Create a custom namespace
            LOGD("Creating custom namespace for mods");
            mod_namespace = android_create_namespace_func(
                "mod_namespace",
                "/data/data:/system/lib64:/system/lib:/vendor/lib64:/vendor/lib",
                "/system/lib64:/system/lib:/vendor/lib64:/vendor/lib",
                ANDROID_NAMESPACE_TYPE_SHARED | ANDROID_NAMESPACE_TYPE_ISOLATED,
                "/data/data:/system/:/vendor/",
                nullptr
            );
            
            if (mod_namespace) {
                LOGI("Successfully created custom mod namespace");
                return true;
            } else {
                LOGE("Failed to create custom mod namespace");
            }
        }
        
        if (android_get_exported_namespace_func) {
            // Try to get an existing namespace that allows more permissive loading
            LOGD("Trying to get vndk namespace");
            mod_namespace = android_get_exported_namespace_func("vndk");
            
            if (!mod_namespace) {
                LOGD("Trying to get sphal namespace");
                mod_namespace = android_get_exported_namespace_func("sphal");
            }
            
            if (mod_namespace) {
                LOGI("Successfully obtained existing namespace for mods");
                return true;
            }
        }
        
        LOGE("Failed to create or obtain namespace for mods");
        return false;
    }
    
public:
    bool initialize() {
        if (!initializeNamespaceFunctions()) {
            return false;
        }
        
        return createModNamespace();
    }
    
    void* loadLibrary(const std::string& path, bool global = true) {
        // Check if already loaded
        auto it = loaded_libraries.find(path);
        if (it != loaded_libraries.end()) {
            LOGD("Library %s already loaded", path.c_str());
            return it->second;
        }
        
        void* handle = nullptr;
        
        if (mod_namespace && android_dlopen_ext_func) {
            // Use namespace-aware loading
            android_dlextinfo extinfo = {};
            extinfo.flags = ANDROID_DLEXT_USE_NAMESPACE;
            extinfo.library_namespace = mod_namespace;
            
            int flags = RTLD_NOW;
            if (global) {
                flags |= RTLD_GLOBAL; // Make symbols available to other libraries
            }
            
            LOGD("Loading library %s with namespace and flags 0x%x", path.c_str(), flags);
            handle = android_dlopen_ext_func(path.c_str(), flags, &extinfo);
            
            if (handle) {
                LOGI("Successfully loaded %s using android_dlopen_ext", path.c_str());
            } else {
                LOGE("Failed to load %s using android_dlopen_ext: %s", path.c_str(), dlerror());
            }
        }
        
        // Fallback to regular dlopen
        if (!handle) {
            LOGD("Falling back to regular dlopen for %s", path.c_str());
            int flags = RTLD_NOW;
            if (global) {
                flags |= RTLD_GLOBAL;
            }
            handle = dlopen(path.c_str(), flags);
            
            if (handle) {
                LOGI("Successfully loaded %s using dlopen", path.c_str());
            } else {
                LOGE("Failed to load %s using dlopen: %s", path.c_str(), dlerror());
            }
        }
        
        if (handle) {
            loaded_libraries[path] = handle;
        }
        
        return handle;
    }
    
    std::vector<std::string> getDependencies(const std::string& libPath) {
        std::vector<std::string> dependencies;

        // Use readelf-like approach to get dependencies
        // For now, we'll use a simple hardcoded mapping for known dependencies
        std::string libName = libPath.substr(libPath.find_last_of('/') + 1);

        if (libName == "libDynamicLights.so") {
            dependencies.push_back("libCrestHelper.so");
        }

        return dependencies;
    }

    bool loadModWithDependencies(const std::string& modPath) {
        std::string modDir = modPath.substr(0, modPath.find_last_of('/'));
        std::string modName = modPath.substr(modPath.find_last_of('/') + 1);

        LOGI("Loading mod: %s", modName.c_str());

        // Get dependencies for this mod
        std::vector<std::string> dependencies = getDependencies(modPath);

        // Load dependencies first with RTLD_GLOBAL
        for (const std::string& dep : dependencies) {
            std::string depPath = modDir + "/" + dep;
            LOGI("Loading dependency: %s", dep.c_str());

            void* depHandle = loadLibrary(depPath, true); // Load with RTLD_GLOBAL
            if (!depHandle) {
                LOGE("Failed to load dependency %s", dep.c_str());
                return false;
            }
        }

        // Now load the main mod
        LOGI("Loading main mod: %s", modName.c_str());
        void* modHandle = loadLibrary(modPath, false); // Don't need GLOBAL for the dependent lib

        return modHandle != nullptr;
    }
    
    void unloadAll() {
        for (auto& pair : loaded_libraries) {
            if (pair.second) {
                dlclose(pair.second);
            }
        }
        loaded_libraries.clear();
    }
};

// Global instance
static std::unique_ptr<NativeModLoader> g_mod_loader;

extern "C" {

JNIEXPORT jboolean JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeInitialize(JNIEnv *env, jclass clazz) {
    if (!g_mod_loader) {
        g_mod_loader = std::make_unique<NativeModLoader>();
    }
    
    return g_mod_loader->initialize() ? JNI_TRUE : JNI_FALSE;
}

JNIEXPORT jboolean JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeLoadMod(JNIEnv *env, jclass clazz, jstring path) {
    if (!g_mod_loader) {
        LOGE("ModLoader not initialized");
        return JNI_FALSE;
    }
    
    const char* nativePath = env->GetStringUTFChars(path, nullptr);
    if (!nativePath) {
        return JNI_FALSE;
    }
    
    bool result = g_mod_loader->loadModWithDependencies(std::string(nativePath));
    
    env->ReleaseStringUTFChars(path, nativePath);
    return result ? JNI_TRUE : JNI_FALSE;
}

JNIEXPORT void JNICALL
Java_org_levimc_launcher_core_mods_ModNativeLoader_nativeUnloadAll(JNIEnv *env, jclass clazz) {
    if (g_mod_loader) {
        g_mod_loader->unloadAll();
    }
}

} // extern "C"
