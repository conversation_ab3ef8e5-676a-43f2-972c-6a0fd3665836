/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <android/dlext.h>

__BEGIN_DECLS

/*
 * Namespace type for android_create_namespace
 */
enum {
  /* A regular namespace is the namespace with a custom search path that does
   * not impose any restrictions on the location of native libraries.
   */
  ANDROID_NAMESPACE_TYPE_REGULAR = 0,

  /* An isolated namespace requires all the libraries to be on the search path
   * or under permitted_when_isolated_path. The search path is the union of
   * ld_library_path and default_library_path.
   */
  ANDROID_NAMESPACE_TYPE_ISOLATED = 1,

  /* The shared namespace clones the list of libraries of the caller namespace upon creation
   * which means that they are shared between namespaces - the caller namespace and the new one
   * will use the same copy of a library if it was loaded prior to android_create_namespace call.
   *
   * Note that libraries loaded after the namespace is created will not be shared.
   *
   * Shared namespaces can be isolated or regular. Note that they do not inherit the search path nor
   * permitted_when_isolated_path from the caller's namespace.
   */
  ANDROID_NAMESPACE_TYPE_SHARED = 2,

  /* This flag instructs linker to enable grey-list workaround for the namespace.
   * See http://b/26394120 for details.
   */
  ANDROID_NAMESPACE_TYPE_GREYLIST_ENABLED = 0x08000000,

  /* This flag instructs linker to use this namespace as the anonymous
   * namespace. There can be only one anonymous namespace in a process. If there
   * already is an anonymous namespace in the process, using this flag when
   * creating a new namespace causes the creation to fail.
   */
  ANDROID_NAMESPACE_TYPE_ALSO_USED_AS_ANONYMOUS = 0x10000000,
};

/*
 * Creates new linker namespace.
 * ld_library_path and default_library_path represent the search path
 * for the libraries in the namespace.
 *
 * The libraries in the namespace are searched by folowing order:
 * 1. ld_library_path (Think of this as namespace-local LD_LIBRARY_PATH)
 * 2. In directories specified by DT_RUNPATH of the "needed by" binary.
 * 3. deault_library_path (This of this as namespace-local default library path)
 *
 * When type is ANDROID_NAMESPACE_TYPE_ISOLATED the resulting namespace requires all of the
 * libraries to be on the search path or under the permitted_when_isolated_path;
 * the search_path is ld_library_path:default_library_path.
 *
 * permitted_when_isolated_path represents the list of directories where library loading is
 * allowed for isolated namespaces. This parameter is ignored for non-isolated namespaces.
 *
 * When a library is loaded this list is used to check if it belongs to the namespace
 * by comparing the library path with the filename and if the check fails only the
 * system namespace is allowed to load it.
 *
 * Note that this check happens only for libraries loaded by dlopen/android_dlopen_ext
 * functions. The libraries loaded as dependencies of other libraries do not trigger
 * this check.
 *
 * parent_namespace if not null the new namespace will inherit a list of libraries from
 * the parent namespace. Note that parent_namespace will be ignored for isolated namespaces.
 *
 * Upon success this function returns new namespace and null on failure.
 *
 * Important note: Linker takes ownership of ld_library_path, default_library_path,
 * and permitted_when_isolated_path parameter. Caller should not free() the memory
 * after the call.
 *
 * This function will fail if the process is not in debuggable mode and
 * android_get_application_target_sdk_version() returns > 27.
 *
 * Starting from API level 28 this function is no longer supported and will
 * return nullptr. It is kept for backwards compatibility with older API levels.
 *
 * Please use android_create_namespace() function from <android/dlext.h> instead.
 */
struct android_namespace_t* android_create_namespace(const char* name,
                                                     const char* ld_library_path,
                                                     const char* default_library_path,
                                                     uint64_t type,
                                                     const char* permitted_when_isolated_path,
                                                     struct android_namespace_t* parent_namespace);

/*
 * Creates a link between namespaces. Every link has list of sonames of
 * shared libraries. These are the libraries which are accessible from
 * namespace 'from' but loaded within namespace 'to' context.
 * When the list of sonames is empty the link serves as an access to
 * all libraries loaded in namespace 'to' from namespace 'from'.
 *
 * The lookup order of the libraries in namespaces with links is following:
 * 1. Look inside current namespace using 'this' namespace search path.
 * 2. Look in linked namespaces
 * 2.1. Perform soname check if sonames list is not empty (see below).
 * 2.2. Search library using linked namespace search path.
 *
 * Note that library will be loaded in the namespace where it was found.
 *
 * sonames parameter is a list of library names separated by ':'. For example:
 * "libc.so:libm.so:libdl.so". If the list is empty, any library can be accessed
 * from the linked namespace.
 *
 * This function will fail if the process is not in debuggable mode and
 * android_get_application_target_sdk_version() returns > 27.
 *
 * Starting from API level 28 this function is no longer supported and will
 * return false. It is kept for backwards compatibility with older API levels.
 *
 * Please use android_link_namespaces() function from <android/dlext.h> instead.
 */
bool android_link_namespaces(struct android_namespace_t* from,
                              struct android_namespace_t* to,
                              const char* sonames);

/*
 * This function returns the namespace exported from the main executable.
 * If the main executable does not export a namespace or if the main
 * executable was not linked with libc.so this function will return null.
 *
 * This function will fail if the process is not in debuggable mode and
 * android_get_application_target_sdk_version() returns > 27.
 *
 * Starting from API level 28 this function is no longer supported and will
 * return nullptr. It is kept for backwards compatibility with older API levels.
 *
 * Please use android_get_exported_namespace() function from <android/dlext.h> instead.
 */
struct android_namespace_t* android_get_exported_namespace(const char* name);

/*
 * This function initializes the anonymous namespace for the current process.
 * Anonymous namespace is used by the linker in case when linker was not able
 * to identify the caller of dlopen/dlsym. This happens for the code not loaded
 * by dynamic linker; for example calls from the executable or calls from
 * runtime-generated code.
 *
 * The anonymous namespace is designated by the name "[anon:<calling_pid>]" and
 * it inherits default.library.path from the caller.
 *
 * Note that this function is intended to be called by libc during the call
 * to dlopen() or dlsym().
 *
 * This function will fail if the process is not in debuggable mode and
 * android_get_application_target_sdk_version() returns > 27.
 *
 * Starting from API level 28 this function is no longer supported and will
 * return false. It is kept for backwards compatibility with older API levels.
 */
bool android_init_anonymous_namespace(const char* shared_libs_sonames,
                                      const char* library_search_path);

__END_DECLS
