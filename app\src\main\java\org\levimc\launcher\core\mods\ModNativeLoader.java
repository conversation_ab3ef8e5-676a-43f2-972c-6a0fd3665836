package org.levimc.launcher.core.mods;

import android.annotation.SuppressLint;

import org.levimc.launcher.util.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public class ModNativeLoader {
    private static boolean nativeLoaderInitialized = false;

    // Native method declarations
    private static native boolean nativeInitialize();
    private static native boolean nativeLoadMod(String path);
    private static native void nativeUnloadAll();

    // Simple mod loader methods (fallback)
    private static native boolean nativeLoadModSimple(String path);
    private static native void nativeUnloadAllSimple();

    /**
     * Initialize the native mod loader with namespace bypass capabilities
     */
    private static boolean initializeNativeLoader() {
        if (nativeLoaderInitialized) {
            return true;
        }

        try {
            nativeLoaderInitialized = nativeInitialize();
            if (nativeLoaderInitialized) {
                Logger.get().info("Native mod loader initialized successfully");
            } else {
                Logger.get().error("Failed to initialize native mod loader");
            }
        } catch (UnsatisfiedLinkError e) {
            Logger.get().error("Native mod loader not available: " + e.getMessage());
            nativeLoaderInitialized = false;
        }

        return nativeLoaderInitialized;
    }

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    public static void loadEnabledSoMods(ModManager modManager, File cacheDir) {
        List<Mod> mods = modManager.getMods();

        // Try to initialize native loader first
        boolean useNativeLoader = initializeNativeLoader();

        for (Mod mod : mods) {
            if (!mod.isEnabled()) continue;
            File src = new File(modManager.getCurrentVersion().modsDir, mod.getFileName());
            File dir = new File(cacheDir, "mods");
            if (!dir.exists()) dir.mkdirs();
            File dst = new File(dir, mod.getFileName());

            try {
                copyFile(src, dst);

                boolean loadSuccess = false;

                if (useNativeLoader) {
                    // Try advanced native loader with namespace bypass first
                    Logger.get().info("Loading " + dst.getName() + " using advanced native loader");
                    loadSuccess = nativeLoadMod(dst.getAbsolutePath());

                    if (loadSuccess) {
                        Logger.get().info("Successfully loaded " + dst.getName() + " with advanced native loader");
                    } else {
                        Logger.get().info("Advanced native loader failed for " + dst.getName() + ", trying simple loader");

                        // Try simple native loader as fallback
                        try {
                            loadSuccess = nativeLoadModSimple(dst.getAbsolutePath());
                            if (loadSuccess) {
                                Logger.get().info("Successfully loaded " + dst.getName() + " with simple native loader");
                            } else {
                                Logger.get().error("Simple native loader also failed for " + dst.getName());
                            }
                        } catch (UnsatisfiedLinkError e) {
                            Logger.get().error("Simple native loader not available: " + e.getMessage());
                        }
                    }
                }

                // Fallback to System.load if native loader failed or not available
                if (!loadSuccess) {
                    System.load(dst.getAbsolutePath());
                    Logger.get().info("Loaded so: " + dst.getName() + " (fallback method)");
                }

            } catch (IOException | UnsatisfiedLinkError e) {
                Logger.get().error("Can't load " + src.getName() + ": " + e.getMessage());
            }
        }
    }

    private static void copyFile(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src);
             OutputStream out = new FileOutputStream(dst)) {
            byte[] buf = new byte[8192];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
        }
    }
}
